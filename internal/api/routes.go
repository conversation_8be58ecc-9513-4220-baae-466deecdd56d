package api

import (
	"github.com/gorilla/mux"
)

func SetupRoutes() *mux.Router {
	r := mux.NewRouter()

	// Type routes
	r.<PERSON>leFunc("/types", GetTypes).Methods("GET")
	r.HandleFunc("/types/{id}", GetType).Methods("GET")
	r.<PERSON>le<PERSON>unc("/types", CreateType).Methods("POST")
	r.HandleFunc("/types/{id}", UpdateType).Methods("PUT")
	r.HandleFunc("/types/{id}", DeleteType).Methods("DELETE")

	// Pet routes
	r.HandleFunc("/pets", GetPets).Methods("GET")
	r.Handle<PERSON>unc("/pets/{id}", GetPet).Methods("GET")
	r.HandleFunc("/pets", CreatePet).Methods("POST")
	r.HandleFunc("/pets/{id}", UpdatePet).Methods("PUT")
	r.<PERSON>un<PERSON>("/pets/{id}", DeletePet).Methods("DELETE")

	// Owner routes
	r.HandleFunc("/owners", GetOwners).Methods("GET")
	r.HandleFunc("/owners/{id}", GetOwner).Methods("GET")
	r.HandleFunc("/owners", CreateOwner).Methods("POST")
	r.HandleFunc("/owners/{id}", UpdateOwner).Methods("PUT")
	r.HandleFunc("/owners/{id}", DeleteOwner).Methods("DELETE")

	// Search routes
	r.HandleFunc("/pets/color/{color}", GetPetsByColor).Methods("GET")
	r.HandleFunc("/pets/owner/{name}", GetPetsByOwnerName).Methods("GET")

	return r
}
