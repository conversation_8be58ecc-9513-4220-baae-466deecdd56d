package api

import (
    "github.com/gorilla/mux"
)

func SetupRoutes() *mux.Router {
    r := mux.NewRouter()

    // Type routes
    r.<PERSON>le<PERSON>unc("/types", GetTypes).Methods("GET")
    r.<PERSON>le<PERSON>unc("/types/{id}", GetType).Methods("GET")
    r.<PERSON>("/types", CreateType).Methods("POST")
    r.HandleFunc("/types/{id}", UpdateType).Methods("PUT")
    r.HandleFunc("/types/{id}", DeleteType).Methods("DELETE")

    // Pet routes
    r.HandleFunc("/pets", GetPets).Methods("GET")
    // Add other CRUD routes for pets
    
    // Owner routes
    // Add CRUD routes for owners
    
    // Search routes
    r.HandleFunc("/pets/color/{color}", GetPetsByColor).Methods("GET")
    r.<PERSON><PERSON>un<PERSON>("/pets/owner/{name}", GetPetsByOwnerName).Methods("GET")

    return r
}