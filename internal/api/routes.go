package api

import (
	"github.com/gorilla/mux"
)

func SetupRoutes() *mux.Router {
	r := mux.NewRouter()

	// Type routes
	r.HandleFunc("/types", GetTypes).Methods("GET")
	r.HandleFunc("/types/{id}", GetType).Methods("GET")
	r.<PERSON>("/types", CreateType).Methods("POST")
	r.HandleFunc("/types/{id}", UpdateType).Methods("PUT")
	r.HandleFunc("/types/{id}", DeleteType).Methods("DELETE")

	// Pet routes
	r.HandleFunc("/pets", GetPets).Methods("GET")
	// Add other CRUD routes for pets

	// Owner routes
	r.HandleFunc("/owners", GetOwners).Methods("GET")
	r.HandleFunc("/owners/{id}", GetOwner).Methods("GET")
	r.<PERSON>("/owners", CreateOwner).Methods("POST")
	r.<PERSON>le<PERSON>un<PERSON>("/owners/{id}", UpdateOwner).Methods("PUT")
	r.<PERSON>le<PERSON>("/owners/{id}", DeleteOwner).Methods("DELETE")

	// Search routes
	r.HandleFunc("/pets/color/{color}", GetPetsByColor).Methods("GET")
	r.HandleFunc("/pets/owner/{name}", GetPetsByOwnerName).Methods("GET")

	return r
}
