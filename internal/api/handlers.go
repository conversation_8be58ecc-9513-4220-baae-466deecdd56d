package api

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"

	"pet-service/internal/db"
	"pet-service/internal/models"

	"github.com/gorilla/mux"
)

// Type handlers
func GetTypes(w http.ResponseWriter, r *http.Request) {
	rows, err := db.DB.Query("SELECT id, name, number_of_legs FROM types")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	types := []models.Type{}
	for rows.Next() {
		var t models.Type
		if err := rows.Scan(&t.ID, &t.Name, &t.NumberOfLegs); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		types = append(types, t)
	}

	json.NewEncoder(w).Encode(types)
}

func GetType(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	var t models.Type
	err = db.DB.QueryRow("SELECT id, name, number_of_legs FROM types WHERE id = $1", id).
		Scan(&t.ID, &t.Name, &t.NumberOfLegs)
	if err == sql.ErrNoRows {
		http.Error(w, "Type not found", http.StatusNotFound)
		return
	} else if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(t)
}

func CreateType(w http.ResponseWriter, r *http.Request) {
	var t models.Type
	if err := json.NewDecoder(r.Body).Decode(&t); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	err := db.DB.QueryRow(
		"INSERT INTO types (name, number_of_legs) VALUES ($1, $2) RETURNING id",
		t.Name, t.NumberOfLegs,
	).Scan(&t.ID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(t)
}

func UpdateType(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	var t models.Type
	if err := json.NewDecoder(r.Body).Decode(&t); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	t.ID = id

	_, err = db.DB.Exec(
		"UPDATE types SET name = $1, number_of_legs = $2 WHERE id = $3",
		t.Name, t.NumberOfLegs, t.ID,
	)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(t)
}

func DeleteType(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	_, err = db.DB.Exec("DELETE FROM types WHERE id = $1", id)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// Pet handlers
func GetPets(w http.ResponseWriter, r *http.Request) {
	rows, err := db.DB.Query("SELECT id, type_id, owner_id, name, color FROM pets")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	pets := []models.Pet{}
	for rows.Next() {
		var p models.Pet
		if err := rows.Scan(&p.ID, &p.TypeID, &p.OwnerID, &p.Name, &p.Color); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		pets = append(pets, p)
	}

	json.NewEncoder(w).Encode(pets)
}

func GetPet(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	var p models.Pet
	err = db.DB.QueryRow("SELECT id, type_id, owner_id, name, color FROM pets WHERE id = $1", id).
		Scan(&p.ID, &p.TypeID, &p.OwnerID, &p.Name, &p.Color)
	if err == sql.ErrNoRows {
		http.Error(w, "Pet not found", http.StatusNotFound)
		return
	} else if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(p)
}

func CreatePet(w http.ResponseWriter, r *http.Request) {
	var p models.Pet
	if err := json.NewDecoder(r.Body).Decode(&p); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	err := db.DB.QueryRow(
		"INSERT INTO pets (type_id, owner_id, name, color) VALUES ($1, $2, $3, $4) RETURNING id",
		p.TypeID, p.OwnerID, p.Name, p.Color,
	).Scan(&p.ID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(p)
}

func UpdatePet(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	var p models.Pet
	if err := json.NewDecoder(r.Body).Decode(&p); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	p.ID = id

	_, err = db.DB.Exec(
		"UPDATE pets SET type_id = $1, owner_id = $2, name = $3, color = $4 WHERE id = $5",
		p.TypeID, p.OwnerID, p.Name, p.Color, p.ID,
	)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(p)
}

func DeletePet(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	_, err = db.DB.Exec("DELETE FROM pets WHERE id = $1", id)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// Additional handlers for search functionality
func GetPetsByColor(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	color := params["color"]

	rows, err := db.DB.Query("SELECT id, type_id, owner_id, name, color FROM pets WHERE color = $1", color)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	pets := []models.Pet{}
	for rows.Next() {
		var p models.Pet
		if err := rows.Scan(&p.ID, &p.TypeID, &p.OwnerID, &p.Name, &p.Color); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		pets = append(pets, p)
	}

	json.NewEncoder(w).Encode(pets)
}

func GetPetsByOwnerName(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	ownerName := params["name"]

	rows, err := db.DB.Query(`
        SELECT p.id, p.type_id, p.owner_id, p.name, p.color 
        FROM pets p
        JOIN owners o ON p.owner_id = o.id
        WHERE o.name = $1
    `, ownerName)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	pets := []models.Pet{}
	for rows.Next() {
		var p models.Pet
		if err := rows.Scan(&p.ID, &p.TypeID, &p.OwnerID, &p.Name, &p.Color); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		pets = append(pets, p)
	}

	json.NewEncoder(w).Encode(pets)
}

// Owner handlers
func GetOwners(w http.ResponseWriter, r *http.Request) {
	rows, err := db.DB.Query("SELECT id, name, age, gender, phone_number FROM owners")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	owners := []models.Owner{}
	for rows.Next() {
		var o models.Owner
		if err := rows.Scan(&o.ID, &o.Name, &o.Age, &o.Gender, &o.PhoneNumber); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		owners = append(owners, o)
	}

	json.NewEncoder(w).Encode(owners)
}

func GetOwner(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	var o models.Owner
	err = db.DB.QueryRow("SELECT id, name, age, gender, phone_number FROM owners WHERE id = $1", id).
		Scan(&o.ID, &o.Name, &o.Age, &o.Gender, &o.PhoneNumber)
	if err == sql.ErrNoRows {
		http.Error(w, "Owner not found", http.StatusNotFound)
		return
	} else if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(o)
}

func CreateOwner(w http.ResponseWriter, r *http.Request) {
	var o models.Owner
	if err := json.NewDecoder(r.Body).Decode(&o); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	err := db.DB.QueryRow(
		"INSERT INTO owners (name, age, gender, phone_number) VALUES ($1, $2, $3, $4) RETURNING id",
		o.Name, o.Age, o.Gender, o.PhoneNumber,
	).Scan(&o.ID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(o)
}

func UpdateOwner(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	var o models.Owner
	if err := json.NewDecoder(r.Body).Decode(&o); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	o.ID = id

	_, err = db.DB.Exec(
		"UPDATE owners SET name = $1, age = $2, gender = $3, phone_number = $4 WHERE id = $5",
		o.Name, o.Age, o.Gender, o.PhoneNumber, o.ID,
	)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(o)
}

func DeleteOwner(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id, err := strconv.Atoi(params["id"])
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	_, err = db.DB.Exec("DELETE FROM owners WHERE id = $1", id)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}
