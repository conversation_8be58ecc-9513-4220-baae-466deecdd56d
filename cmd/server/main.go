package main

import (
    "log"
    "net/http"
    "os"

    "pet-service/internal/api"
    "pet-service/internal/db"

    "github.com/joho/godotenv"
)

func main() {
    // Load environment variables
    if err := godotenv.Load(); err != nil {
        log.Println("No .env file found, using environment variables")
    }

    // Initialize database connection
    db.InitDB()

    // Setup routes
    router := api.SetupRoutes()

    // Start server
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }

    log.Printf("Server starting on port %s", port)
    log.Fatal(http.ListenAndServe(":"+port, router))
}