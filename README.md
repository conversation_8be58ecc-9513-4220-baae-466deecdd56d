# Pet Service API

A RESTful API service for managing pets, their types, and owners.

## Getting Started

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose

### Running the Application
```bash
docker-compose up -d
```

The API will be available at http://localhost:8080

## Data Models

### Type
```json
{
  "id": 1,
  "name": "<PERSON>",
  "numberOfLegs": 4
}
```

### Owner
```json
{
  "id": 1,
  "name": "<PERSON>",
  "age": 30,
  "gender": "Male",
  "phoneNumber": "555-1234"
}
```

### Pet
```json
{
  "id": 1,
  "typeId": 1,
  "ownerId": 1,
  "name": "<PERSON>",
  "color": "<PERSON>"
}
```

## API Endpoints

### Types ✅ (Full CRUD Implementation)
- `GET /types` - Get all types
- `GET /types/{id}` - Get a specific type
- `POST /types` - Create a new type
- `PUT /types/{id}` - Update a type
- `DELETE /types/{id}` - Delete a type

### Owners ✅ (Full CRUD Implementation)
- `GET /owners` - Get all owners
- `GET /owners/{id}` - Get a specific owner
- `POST /owners` - Create a new owner
- `PUT /owners/{id}` - Update an owner
- `DELETE /owners/{id}` - Delete an owner

### Pets ✅ (Full CRUD Implementation)
- `GET /pets` - Get all pets
- `GET /pets/{id}` - Get a specific pet
- `POST /pets` - Create a new pet
- `PUT /pets/{id}` - Update a pet
- `DELETE /pets/{id}` - Delete a pet

### Search
- `GET /pets/color/{color}` - Get all pets of a specific color
- `GET /pets/owner/{name}` - Get all pets belonging to an owner with the specified name

## Testing with cURL

### Types Examples

#### Create a new type
```bash
curl -X POST http://localhost:8080/types \
  -H "Content-Type: application/json" \
  -d '{"name": "Hamster", "numberOfLegs": 4}'
```

#### Get all types
```bash
curl -X GET http://localhost:8080/types
```

#### Get a specific type
```bash
curl -X GET http://localhost:8080/types/1
```

#### Update a type
```bash
curl -X PUT http://localhost:8080/types/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Dog", "numberOfLegs": 4}'
```

#### Delete a type
```bash
curl -X DELETE http://localhost:8080/types/1
```

### Owners Examples ✨ (Full CRUD)

#### Create a new owner
```bash
curl -X POST http://localhost:8080/owners \
  -H "Content-Type: application/json" \
  -d '{"name": "Sarah", "age": 28, "gender": "Female", "phoneNumber": "555-4321"}'
```

#### Get all owners
```bash
curl -X GET http://localhost:8080/owners
```

#### Get a specific owner
```bash
curl -X GET http://localhost:8080/owners/1
```

#### Update an owner
```bash
curl -X PUT http://localhost:8080/owners/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "Sarah Johnson", "age": 29, "gender": "Female", "phoneNumber": "555-4321"}'
```

#### Delete an owner
```bash
curl -X DELETE http://localhost:8080/owners/1
```

### Pets Examples ✅ (Full CRUD)

#### Get all pets
```bash
curl -X GET http://localhost:8080/pets
```

#### Get a specific pet
```bash
curl -X GET http://localhost:8080/pets/1
```

#### Create a new pet
```bash
curl -X POST http://localhost:8080/pets \
  -H "Content-Type: application/json" \
  -d '{"typeId": 1, "ownerId": 2, "name": "Max", "color": "Black"}'
```

#### Update a pet
```bash
curl -X PUT http://localhost:8080/pets/1 \
  -H "Content-Type: application/json" \
  -d '{"typeId": 1, "ownerId": 2, "name": "Max", "color": "Brown"}'
```

#### Delete a pet
```bash
curl -X DELETE http://localhost:8080/pets/1
```

### Search Examples

#### Search for black pets
```bash
curl -X GET http://localhost:8080/pets/color/Black
```

#### Search for pets owned by Amy
```bash
curl -X GET http://localhost:8080/pets/owner/Amy
```

## Development Status

- ✅ **Types**: Full CRUD implementation complete
- ✅ **Owners**: Full CRUD implementation complete
- ✅ **Pets**: Full CRUD implementation complete
- ✅ **Search**: Color and owner name search implemented

🎉 **All entities now have complete CRUD operations implemented!**

## Database Schema

The application uses PostgreSQL with the following tables:

- **types**: `id`, `name`, `number_of_legs`
- **owners**: `id`, `name`, `age`, `gender`, `phone_number`
- **pets**: `id`, `type_id`, `owner_id`, `name`, `color`

Sample data is automatically loaded when the database is initialized.