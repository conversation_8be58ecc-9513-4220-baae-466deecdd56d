# Pet Service API

A RESTful API service for managing pets, their types, and owners.

## Getting Started

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose

### Running the Application
```bash
docker-compose up -d
```

The API will be available at http://localhost:8080

## API Endpoints

### Types
- `GET /types` - Get all types
- `GET /types/{id}` - Get a specific type
- `POST /types` - Create a new type
- `PUT /types/{id}` - Update a type
- `DELETE /types/{id}` - Delete a type

### Pets
- `GET /pets` - Get all pets
- `GET /pets/{id}` - Get a specific pet
- `POST /pets` - Create a new pet
- `PUT /pets/{id}` - Update a pet
- `DELETE /pets/{id}` - Delete a pet

### Owners
- `GET /owners` - Get all owners
- `GET /owners/{id}` - Get a specific owner
- `POST /owners` - Create a new owner
- `PUT /owners/{id}` - Update an owner
- `DELETE /owners/{id}` - Delete an owner

### Search
- `GET /pets/color/{color}` - Get all pets of a specific color
- `GET /pets/owner/{name}` - Get all pets belonging to an owner with the specified name

## Testing with cURL

### Create a new type
```bash
curl -X POST http://localhost:8080/types \
  -H "Content-Type: application/json" \
  -d '{"name": "Hamster", "numberOfLegs": 4}'
```

### Create a new owner
```bash
curl -X POST http://localhost:8080/owners \
  -H "Content-Type: application/json" \
  -d '{"name": "Sarah", "age": 28, "gender": "Female", "phoneNumber": "555-4321"}'
```

### Create a new pet
```bash
curl -X POST http://localhost:8080/pets \
  -H "Content-Type: application/json" \
  -d '{"typeId": 1, "ownerId": 2, "name": "Max", "color": "Black"}'
```

### Get all pets
```bash
curl -X GET http://localhost:8080/pets
```

### Get a specific pet
```bash
curl -X GET http://localhost:8080/pets/1
```

### Update a pet
```bash
curl -X PUT http://localhost:8080/pets/1 \
  -H "Content-Type: application/json" \
  -d '{"typeId": 1, "ownerId": 2, "name": "Max", "color": "Brown"}'
```

### Delete a pet
```bash
curl -X DELETE http://localhost:8080/pets/1
```

### Search for black pets
```bash
curl -X GET http://localhost:8080/pets/color/Black
```

### Search for pets owned by Amy
```bash
curl -X GET http://localhost:8080/pets/owner/Amy
```